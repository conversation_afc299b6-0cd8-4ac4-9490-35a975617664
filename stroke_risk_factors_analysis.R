# 脑卒中风险因素数据库分析
# 分析影响脑卒中的风险因素关系，以及全球和中国分别的影响

library(dplyr)
library(readr)
library(ggplot2)
library(tidyr)
library(gridExtra)
library(scales)

# 设置中文字体
if (.Platform$OS.type == "windows") {
  windowsFonts(SimSun = windowsFont("SimSun"))
  par(family = "SimSun")
}

cat("=== 脑卒中风险因素数据库分析 ===\n")

# 1. 扫描风险因素数据库文件
cat("1. 扫描风险因素数据库文件...\n")

# 扫描204国家数据文件
country_files <- list.files("风险因素-数据库/204国家", recursive = TRUE, pattern = "\\.csv$", full.names = TRUE)
cat("找到204国家风险因素数据文件:", length(country_files), "个\n")

# 扫描GBD region数据文件
region_files <- list.files("风险因素-数据库/gbd region", recursive = TRUE, pattern = "\\.csv$", full.names = TRUE)
cat("找到GBD region风险因素数据文件:", length(region_files), "个\n")

# 扫描GBD-SD数据文件
sdi_files <- list.files("风险因素-数据库/gbd-sd", recursive = TRUE, pattern = "\\.csv$", full.names = TRUE)
cat("找到GBD-SD风险因素数据文件:", length(sdi_files), "个\n")

# 2. 读取第一个文件来了解数据结构
cat("\n2. 分析数据结构...\n")

if(length(country_files) > 0) {
  tryCatch({
    # 读取第一个文件
    sample_data <- read_csv(country_files[1], show_col_types = FALSE)
    cat("数据列名:\n")
    print(colnames(sample_data))
    cat("\n数据维度:", nrow(sample_data), "行,", ncol(sample_data), "列\n")
    
    # 显示前几行数据
    cat("\n前5行数据预览:\n")
    print(head(sample_data, 5))
    
    # 检查风险因素类型
    if("rei_name" %in% colnames(sample_data)) {
      risk_factors <- unique(sample_data$rei_name)
      cat("\n风险因素类型 (", length(risk_factors), "种):\n")
      for(i in 1:min(10, length(risk_factors))) {
        cat(i, ".", risk_factors[i], "\n")
      }
      if(length(risk_factors) > 10) {
        cat("... 还有", length(risk_factors) - 10, "种风险因素\n")
      }
    }
    
    # 检查疾病类型
    if("cause_name" %in% colnames(sample_data)) {
      causes <- unique(sample_data$cause_name)
      cat("\n疾病类型 (", length(causes), "种):\n")
      for(i in 1:min(5, length(causes))) {
        cat(i, ".", causes[i], "\n")
      }
    }
    
    # 检查地区
    if("location_name" %in% colnames(sample_data)) {
      locations <- unique(sample_data$location_name)
      cat("\n地区数量:", length(locations), "\n")
      # 查找中国数据
      china_locations <- locations[grepl("China|中国", locations, ignore.case = TRUE)]
      if(length(china_locations) > 0) {
        cat("中国相关地区:\n")
        print(china_locations)
      }
    }
    
    # 检查年份范围
    if("year" %in% colnames(sample_data)) {
      years <- range(sample_data$year, na.rm = TRUE)
      cat("\n年份范围:", years[1], "-", years[2], "\n")
    }
    
    # 检查指标类型
    if("measure_name" %in% colnames(sample_data)) {
      measures <- unique(sample_data$measure_name)
      cat("\n指标类型:\n")
      print(measures)
    }
    
  }, error = function(e) {
    cat("读取文件时出错:", e$message, "\n")
    cat("尝试使用不同的编码...\n")
    
    # 尝试不同编码
    tryCatch({
      sample_data <- read.csv(country_files[1], fileEncoding = "UTF-8")
      cat("使用UTF-8编码成功读取\n")
      cat("数据维度:", nrow(sample_data), "行,", ncol(sample_data), "列\n")
      print(colnames(sample_data))
    }, error = function(e2) {
      cat("UTF-8编码也失败:", e2$message, "\n")
    })
  })
}

# 3. 创建风险因素分析函数
analyze_risk_factors <- function(file_path) {
  tryCatch({
    data <- read_csv(file_path, show_col_types = FALSE)
    
    # 筛选脑卒中相关数据
    if("cause_name" %in% colnames(data)) {
      stroke_data <- data %>%
        filter(grepl("stroke|Stroke|脑卒中", cause_name, ignore.case = TRUE))
      
      if(nrow(stroke_data) > 0) {
        return(stroke_data)
      }
    }
    
    return(NULL)
  }, error = function(e) {
    return(NULL)
  })
}

# 4. 分析所有文件中的脑卒中风险因素数据
cat("\n3. 分析脑卒中风险因素数据...\n")

all_stroke_risk_data <- list()
file_counter <- 0

# 分析204国家数据
for(file in country_files[1:min(5, length(country_files))]) {  # 先分析前5个文件
  cat("分析文件:", basename(file), "\n")
  stroke_data <- analyze_risk_factors(file)
  if(!is.null(stroke_data)) {
    file_counter <- file_counter + 1
    all_stroke_risk_data[[file_counter]] <- stroke_data
    cat("  找到脑卒中数据:", nrow(stroke_data), "行\n")
  }
}

if(length(all_stroke_risk_data) > 0) {
  # 合并所有数据
  combined_stroke_risk_data <- bind_rows(all_stroke_risk_data)
  cat("\n合并后的脑卒中风险因素数据:", nrow(combined_stroke_risk_data), "行\n")
  
  # 保存合并数据
  write_csv(combined_stroke_risk_data, "stroke_risk_factors_combined_data.csv")
  
  # 分析风险因素
  if("rei_name" %in% colnames(combined_stroke_risk_data)) {
    risk_factors_summary <- combined_stroke_risk_data %>%
      group_by(rei_name) %>%
      summarise(
        data_points = n(),
        countries = n_distinct(location_name, na.rm = TRUE),
        years = n_distinct(year, na.rm = TRUE),
        .groups = 'drop'
      ) %>%
      arrange(desc(data_points))
    
    cat("\n主要脑卒中风险因素:\n")
    print(head(risk_factors_summary, 10))
    
    # 保存风险因素摘要
    write_csv(risk_factors_summary, "stroke_risk_factors_summary.csv")
  }
  
  # 分析中国vs全球数据
  if("location_name" %in% colnames(combined_stroke_risk_data)) {
    china_data <- combined_stroke_risk_data %>%
      filter(grepl("China|中国", location_name, ignore.case = TRUE))
    
    global_data <- combined_stroke_risk_data %>%
      filter(grepl("Global|全球", location_name, ignore.case = TRUE))
    
    cat("\n中国脑卒中风险因素数据:", nrow(china_data), "行\n")
    cat("全球脑卒中风险因素数据:", nrow(global_data), "行\n")
    
    if(nrow(china_data) > 0 && nrow(global_data) > 0) {
      # 创建中国vs全球对比分析
      china_vs_global_analysis <- bind_rows(
        china_data %>% mutate(region_type = "中国"),
        global_data %>% mutate(region_type = "全球")
      )
      
      write_csv(china_vs_global_analysis, "stroke_risk_factors_china_vs_global.csv")
      cat("中国vs全球对比数据已保存\n")
    }
  }
  
} else {
  cat("未找到脑卒中相关的风险因素数据\n")
  cat("尝试分析所有数据以了解内容...\n")
  
  # 如果没找到脑卒中数据，分析第一个文件的所有内容
  if(length(country_files) > 0) {
    tryCatch({
      all_data <- read_csv(country_files[1], show_col_types = FALSE)
      
      if("cause_name" %in% colnames(all_data)) {
        all_causes <- unique(all_data$cause_name)
        cat("\n所有疾病类型:\n")
        for(i in 1:min(20, length(all_causes))) {
          cat(i, ".", all_causes[i], "\n")
        }
      }
      
      if("rei_name" %in% colnames(all_data)) {
        all_risks <- unique(all_data$rei_name)
        cat("\n所有风险因素:\n")
        for(i in 1:min(20, length(all_risks))) {
          cat(i, ".", all_risks[i], "\n")
        }
      }
      
    }, error = function(e) {
      cat("分析所有数据时出错:", e$message, "\n")
    })
  }
}

cat("\n=== 风险因素数据库分析完成 ===\n")
cat("生成的文件:\n")
cat("- stroke_risk_factors_combined_data.csv (如果找到脑卒中数据)\n")
cat("- stroke_risk_factors_summary.csv (风险因素摘要)\n")
cat("- stroke_risk_factors_china_vs_global.csv (中国vs全球对比)\n")
