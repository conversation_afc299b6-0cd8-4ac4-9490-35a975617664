# 脑卒中风险因素综合分析 - 中国与全球对比
# 基于GBD 2021风险因素数据库

library(dplyr)
library(readr)
library(ggplot2)
library(tidyr)
library(gridExtra)
library(scales)
library(RColorBrewer)
library(tibble)

# 设置中文字体
if (.Platform$OS.type == "windows") {
  windowsFonts(SimSun = windowsFont("SimSun"))
  par(family = "SimSun")
}

cat("=== 脑卒中风险因素综合分析 - 中国与全球对比 ===\n")

# 1. 读取已合并的风险因素数据
cat("1. 读取风险因素数据...\n")
stroke_risk_data <- read_csv("stroke_risk_factors_combined_data.csv", show_col_types = FALSE)

cat("总数据量:", nrow(stroke_risk_data), "行\n")
cat("数据时间范围:", min(stroke_risk_data$year), "-", max(stroke_risk_data$year), "\n")

# 2. 数据预处理和筛选
cat("\n2. 数据预处理...\n")

# 筛选中国数据
china_data <- stroke_risk_data %>%
  filter(location_name == "中国")

# 获取全球数据（通过计算所有国家的加权平均）
# 先获取所有国家列表
all_countries <- unique(stroke_risk_data$location_name)
cat("数据库包含国家/地区数:", length(all_countries), "\n")

# 计算全球加权平均（简化处理，使用所有国家的平均值）
global_data <- stroke_risk_data %>%
  group_by(rei_name, year, age_name, sex_name, measure_name, metric_name) %>%
  summarise(
    val = mean(val, na.rm = TRUE),
    upper = mean(upper, na.rm = TRUE),
    lower = mean(lower, na.rm = TRUE),
    .groups = 'drop'
  ) %>%
  mutate(location_name = "全球平均")

cat("中国数据:", nrow(china_data), "行\n")
cat("全球平均数据:", nrow(global_data), "行\n")

# 3. 风险因素影响程度分析
cat("\n3. 分析风险因素影响程度...\n")

# 计算2021年各风险因素的死亡归因数
risk_impact_2021 <- bind_rows(
  china_data %>% mutate(region = "中国"),
  global_data %>% mutate(region = "全球平均")
) %>%
  filter(year == 2021, sex_name == "合计", age_name == "年龄标准化") %>%
  group_by(region, rei_name) %>%
  summarise(val = mean(val, na.rm = TRUE), .groups = 'drop') %>%
  pivot_wider(names_from = region, values_from = val, values_fn = mean) %>%
  filter(!is.na(`中国`) & !is.na(`全球平均`)) %>%
  mutate(
    china_vs_global_ratio = `中国` / `全球平均`,
    risk_level_china = case_when(
      `中国` >= quantile(`中国`, 0.8, na.rm = TRUE) ~ "高风险",
      `中国` >= quantile(`中国`, 0.6, na.rm = TRUE) ~ "中等风险",
      `中国` >= quantile(`中国`, 0.4, na.rm = TRUE) ~ "中低风险",
      TRUE ~ "低风险"
    )
  ) %>%
  arrange(desc(`中国`))

cat("2021年主要风险因素排序（按中国影响程度）:\n")
print(head(risk_impact_2021, 10))

# 4. 时间趋势分析
cat("\n4. 分析时间趋势...\n")

# 计算AAPC
calculate_aapc <- function(data, value_col) {
  if(nrow(data) < 2) return(NA)
  
  years <- data$year
  values <- data[[value_col]]
  
  # 移除NA值
  valid_idx <- !is.na(values) & values > 0
  if(sum(valid_idx) < 2) return(NA)
  
  years <- years[valid_idx]
  values <- values[valid_idx]
  
  # 线性回归
  model <- lm(log(values) ~ years)
  aapc <- (exp(coef(model)[2]) - 1) * 100
  
  return(aapc)
}

# 计算各风险因素的AAPC
risk_trends <- bind_rows(
  china_data %>% mutate(region = "中国"),
  global_data %>% mutate(region = "全球平均")
) %>%
  filter(sex_name == "合计", age_name == "年龄标准化") %>%
  group_by(region, rei_name) %>%
  summarise(
    aapc = calculate_aapc(pick(everything()), "val"),
    value_1990 = val[year == 1990][1],
    value_2021 = val[year == 2021][1],
    .groups = "drop"
  ) %>%
  filter(!is.na(aapc))

cat("风险因素趋势分析完成，包含", nrow(risk_trends), "个数据点\n")

# 5. 创建可视化图表

# 5.1 风险因素影响程度对比图
p1 <- risk_impact_2021 %>%
  filter(!is.na(`中国`) & !is.na(`全球平均`)) %>%
  head(10) %>%
  pivot_longer(cols = c(`中国`, `全球平均`), names_to = "region", values_to = "impact") %>%
  ggplot(aes(x = reorder(rei_name, impact), y = impact, fill = region)) +
  geom_col(position = "dodge", alpha = 0.8) +
  coord_flip() +
  labs(
    title = "2021年主要脑卒中风险因素影响程度对比",
    subtitle = "年龄标准化死亡归因数（每10万人）",
    x = "风险因素",
    y = "影响程度",
    fill = "地区"
  ) +
  scale_fill_manual(values = c("中国" = "#E31A1C", "全球平均" = "#1F78B4")) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5, size = 11),
    legend.position = "bottom",
    axis.text.y = element_text(size = 10)
  )

# 5.2 风险因素趋势对比图
trend_comparison <- risk_trends %>%
  select(region, rei_name, aapc) %>%
  pivot_wider(names_from = region, values_from = aapc) %>%
  filter(!is.na(`中国`) & !is.na(`全球平均`))

p2 <- trend_comparison %>%
  ggplot(aes(x = `全球平均`, y = `中国`)) +
  geom_point(size = 3, alpha = 0.7, color = "#E31A1C") +
  geom_abline(slope = 1, intercept = 0, linetype = "dashed", color = "gray50") +
  geom_text(aes(label = rei_name), vjust = -0.5, hjust = 0.5, size = 3) +
  labs(
    title = "风险因素变化趋势对比 (AAPC, 1990-2021)",
    x = "全球平均 AAPC (%/年)",
    y = "中国 AAPC (%/年)",
    caption = "虚线表示中国与全球趋势一致"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    plot.caption = element_text(size = 10)
  )

# 5.3 中国风险因素热力图
# 转换为长格式用于ggplot
heatmap_long <- china_data %>%
  filter(sex_name == "合计", age_name == "年龄标准化") %>%
  group_by(rei_name, year) %>%
  summarise(val = mean(val, na.rm = TRUE), .groups = "drop")

p3 <- heatmap_long %>%
  ggplot(aes(x = year, y = reorder(rei_name, val), fill = val)) +
  geom_tile() +
  scale_fill_gradient2(
    low = "blue", mid = "white", high = "red",
    midpoint = median(heatmap_long$val, na.rm = TRUE),
    name = "影响程度"
  ) +
  labs(
    title = "中国脑卒中风险因素时间变化热力图 (1990-2021)",
    x = "年份",
    y = "风险因素"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    axis.text.y = element_text(size = 9),
    axis.text.x = element_text(angle = 45, hjust = 1)
  )

# 保存图表
png("stroke_risk_factors_comprehensive_analysis.png", 
    width = 16, height = 12, units = "in", res = 300)

grid.arrange(
  p1,
  arrangeGrob(p2, p3, ncol = 2),
  ncol = 1,
  heights = c(1, 1)
)

dev.off()

# 6. 生成分析报告
cat("\n=== 脑卒中风险因素分析报告 ===\n")

cat("\n1. 主要发现:\n")
cat("- 数据覆盖", length(all_countries), "个国家/地区，时间跨度1990-2021年\n")
cat("- 识别出", length(unique(stroke_risk_data$rei_name)), "个主要风险因素\n")
cat("- 中国数据包含", nrow(china_data), "个数据点\n")

cat("\n2. 2021年中国主要风险因素（前5位）:\n")
top5_china <- head(risk_impact_2021, 5)
for(i in 1:nrow(top5_china)) {
  cat(i, ".", top5_china$rei_name[i], 
      "- 影响程度:", round(top5_china$`中国`[i], 2), 
      "（全球平均:", round(top5_china$`全球平均`[i], 2), "）\n")
}

cat("\n3. 中国vs全球风险因素对比:\n")
higher_in_china <- risk_impact_2021 %>%
  filter(china_vs_global_ratio > 1.2) %>%
  arrange(desc(china_vs_global_ratio))

if(nrow(higher_in_china) > 0) {
  cat("中国明显高于全球平均的风险因素:\n")
  for(i in 1:min(5, nrow(higher_in_china))) {
    cat("- ", higher_in_china$rei_name[i], 
        " (中国/全球比值:", round(higher_in_china$china_vs_global_ratio[i], 2), ")\n")
  }
}

# 保存详细结果
write_csv(risk_impact_2021, "stroke_risk_factors_impact_comparison_2021.csv")
write_csv(risk_trends, "stroke_risk_factors_trends_1990_2021.csv")

cat("\n图表已保存: stroke_risk_factors_comprehensive_analysis.png\n")
cat("详细数据已保存:\n")
cat("- stroke_risk_factors_impact_comparison_2021.csv\n")
cat("- stroke_risk_factors_trends_1990_2021.csv\n")

cat("\n分析完成！\n")
