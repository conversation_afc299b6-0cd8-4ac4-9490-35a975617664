# 脑卒中风险因素可视化优化版 - 重点改进图例显示
# 基于GBD 2021风险因素数据库

library(dplyr)
library(readr)
library(ggplot2)
library(tidyr)
library(gridExtra)
library(scales)
library(RColorBrewer)
library(tibble)

# 注释掉ggrepel，使用基础的geom_text
# library(ggrepel)

# 设置中文字体
if (.Platform$OS.type == "windows") {
  windowsFonts(SimSun = windowsFont("SimSun"))
  par(family = "SimSun")
}

cat("=== 脑卒中风险因素可视化优化版 ===\n")

# 1. 读取数据
stroke_risk_data <- read_csv("stroke_risk_factors_combined_data.csv", show_col_types = FALSE)
risk_impact_2021 <- read_csv("stroke_risk_factors_impact_comparison_2021.csv", show_col_types = FALSE)
risk_trends <- read_csv("stroke_risk_factors_trends_1990_2021.csv", show_col_types = FALSE)

cat("数据读取完成\n")

# 2. 数据预处理
china_data <- stroke_risk_data %>%
  filter(location_name == "中国")

# 计算全球平均数据
global_data <- stroke_risk_data %>%
  group_by(rei_name, year, age_name, sex_name, measure_name, metric_name) %>%
  summarise(
    val = mean(val, na.rm = TRUE),
    upper = mean(upper, na.rm = TRUE),
    lower = mean(lower, na.rm = TRUE),
    .groups = "drop"
  ) %>%
  mutate(location_name = "全球平均")

# 3. 创建优化的可视化图表

# 3.1 风险因素影响程度对比图 - 优化图例
p1 <- risk_impact_2021 %>%
  filter(!is.na(`中国`) & !is.na(`全球平均`)) %>%
  head(10) %>%
  pivot_longer(cols = c(`中国`, `全球平均`), names_to = "region", values_to = "impact") %>%
  ggplot(aes(x = reorder(rei_name, impact), y = impact, fill = region)) +
  geom_col(position = "dodge", alpha = 0.8, width = 0.7) +
  geom_text(aes(label = round(impact, 1)), 
            position = position_dodge(width = 0.7), 
            hjust = -0.1, size = 3.5, fontface = "bold") +
  coord_flip() +
  labs(
    title = "2021年主要脑卒中风险因素影响程度对比",
    subtitle = "年龄标准化死亡归因数（每10万人）",
    x = "风险因素",
    y = "影响程度（每10万人）",
    fill = "地区"
  ) +
  scale_fill_manual(
    values = c("中国" = "#E31A1C", "全球平均" = "#1F78B4"),
    labels = c("中国", "全球平均")
  ) +
  scale_y_continuous(
    labels = comma_format(),
    expand = expansion(mult = c(0, 0.15))
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 16, face = "bold", margin = margin(b = 10)),
    plot.subtitle = element_text(hjust = 0.5, size = 12, color = "gray40"),
    legend.position = "bottom",
    legend.title = element_text(size = 14, face = "bold"),
    legend.text = element_text(size = 12),
    legend.key.size = unit(1.2, "cm"),
    legend.key.width = unit(1.5, "cm"),
    legend.margin = margin(t = 15),
    legend.box.background = element_rect(color = "gray80", fill = "white", linewidth = 0.5),
    legend.box.margin = margin(5, 5, 5, 5),
    axis.title = element_text(size = 13, face = "bold"),
    axis.text = element_text(size = 11),
    axis.text.y = element_text(size = 10),
    panel.grid.minor = element_blank(),
    panel.grid.major.x = element_line(color = "gray90", linewidth = 0.5),
    panel.grid.major.y = element_blank()
  ) +
  guides(
    fill = guide_legend(
      title = "地区对比",
      title.position = "top",
      title.hjust = 0.5,
      label.position = "bottom",
      nrow = 1,
      override.aes = list(alpha = 1, size = 1)
    )
  )

# 3.2 风险因素趋势对比图 - 优化图例和标签
trend_comparison <- risk_trends %>%
  select(region, rei_name, aapc) %>%
  pivot_wider(names_from = region, values_from = aapc) %>%
  filter(!is.na(`中国`) & !is.na(`全球平均`))

p2 <- trend_comparison %>%
  ggplot(aes(x = `全球平均`, y = `中国`)) +
  geom_hline(yintercept = 0, linetype = "dashed", color = "gray60", linewidth = 0.8) +
  geom_vline(xintercept = 0, linetype = "dashed", color = "gray60", linewidth = 0.8) +
  geom_abline(slope = 1, intercept = 0, linetype = "solid", color = "gray40", linewidth = 0.8) +
  geom_point(size = 4, alpha = 0.8, color = "#E31A1C") +
  geom_text(
    aes(label = rei_name),
    size = 3.2,
    fontface = "bold",
    vjust = -0.8,
    hjust = 0.5,
    color = "black"
  ) +
  labs(
    title = "风险因素变化趋势对比 (AAPC, 1990-2021)",
    subtitle = "平均年度百分比变化",
    x = "全球平均 AAPC (%/年)",
    y = "中国 AAPC (%/年)",
    caption = "实线：中国与全球趋势一致\n虚线：零变化参考线"
  ) +
  scale_x_continuous(labels = function(x) paste0(x, "%")) +
  scale_y_continuous(labels = function(x) paste0(x, "%")) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 16, face = "bold", margin = margin(b = 5)),
    plot.subtitle = element_text(hjust = 0.5, size = 12, color = "gray40"),
    plot.caption = element_text(size = 10, color = "gray50", hjust = 0),
    axis.title = element_text(size = 13, face = "bold"),
    axis.text = element_text(size = 11),
    panel.grid.minor = element_blank(),
    panel.grid.major = element_line(color = "gray90", linewidth = 0.5)
  )

# 3.3 中国风险因素热力图 - 大幅优化图例
heatmap_long <- china_data %>%
  filter(sex_name == "合计", age_name == "年龄标准化") %>%
  group_by(rei_name, year) %>%
  summarise(val = mean(val, na.rm = TRUE), .groups = "drop")

# 计算更好的颜色断点
val_range <- range(heatmap_long$val, na.rm = TRUE)
val_breaks <- seq(val_range[1], val_range[2], length.out = 7)

p3 <- heatmap_long %>%
  ggplot(aes(x = year, y = reorder(rei_name, val), fill = val)) +
  geom_tile(color = "white", linewidth = 0.2) +
  scale_fill_gradient2(
    low = "#2166AC", 
    mid = "#F7F7F7", 
    high = "#B2182B",
    midpoint = median(heatmap_long$val, na.rm = TRUE),
    name = "影响程度\n(每10万人)",
    breaks = val_breaks,
    labels = round(val_breaks, 1),
    guide = guide_colorbar(
      title.position = "top",
      title.hjust = 0.5,
      title.theme = element_text(size = 12, face = "bold"),
      label.theme = element_text(size = 10),
      barwidth = 15,
      barheight = 1.2,
      frame.colour = "black",
      frame.linewidth = 0.5,
      ticks.colour = "black",
      ticks.linewidth = 0.5
    )
  ) +
  labs(
    title = "中国脑卒中风险因素时间变化热力图 (1990-2021)",
    subtitle = "颜色深浅表示风险因素影响程度",
    x = "年份",
    y = "风险因素"
  ) +
  scale_x_continuous(
    breaks = seq(1990, 2021, 5),
    expand = c(0, 0)
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 16, face = "bold", margin = margin(b = 5)),
    plot.subtitle = element_text(hjust = 0.5, size = 12, color = "gray40"),
    axis.title = element_text(size = 13, face = "bold"),
    axis.text.y = element_text(size = 10),
    axis.text.x = element_text(size = 10, angle = 45, hjust = 1),
    legend.position = "bottom",
    legend.margin = margin(t = 15),
    legend.box.background = element_rect(color = "gray80", fill = "white", linewidth = 0.5),
    legend.box.margin = margin(5, 5, 5, 5),
    panel.grid = element_blank(),
    axis.ticks = element_line(color = "gray50", linewidth = 0.3)
  )

# 4. 创建增强的组合图表
png("stroke_risk_factors_enhanced_visualization.png", 
    width = 20, height = 16, units = "in", res = 300)

# 使用更好的布局
layout_matrix <- rbind(
  c(1, 1, 1, 1),
  c(2, 2, 3, 3),
  c(2, 2, 3, 3)
)

grid.arrange(
  p1,
  p2,
  p3,
  layout_matrix = layout_matrix,
  heights = c(1.2, 1, 1)
)

dev.off()

# 5. 创建单独的热力图（更大尺寸，更清晰的图例）
png("stroke_risk_factors_heatmap_enhanced.png", 
    width = 14, height = 10, units = "in", res = 300)

print(p3)

dev.off()

cat("\n=== 图表优化完成 ===\n")
cat("生成的优化图表:\n")
cat("- stroke_risk_factors_enhanced_visualization.png (综合图表)\n")
cat("- stroke_risk_factors_heatmap_enhanced.png (单独热力图)\n")
cat("\n图例优化要点:\n")
cat("1. 增大图例尺寸和字体\n")
cat("2. 添加边框和背景\n")
cat("3. 优化颜色条显示\n")
cat("4. 改进标签和标题\n")
cat("5. 增强视觉层次\n")
